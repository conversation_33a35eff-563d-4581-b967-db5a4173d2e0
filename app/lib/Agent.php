<?php

namespace app\lib;

use app\model\Conversation;
use app\model\Message;
use app\model\Space;
use think\ai\Client;
use think\helper\Arr;

class Agent extends \think\agent\Agent
{
    /** @var Conversation */
    protected $conversation;

    protected $isNewConversation = false;

    /** @var Message */
    protected $message;

    public function __construct(protected Space $space)
    {
    }

    protected function initConversation($params)
    {
        $input          = Arr::get($params, 'input');
        $conversationId = Arr::get($params, 'conversation');

        if (!empty($conversationId)) {
            if ($conversationId instanceof Conversation) {
                $conversation = $conversationId;
            } else {
                $conversation = $this->space->conversations()->find($conversationId);
            }
        }

        if (empty($conversation)) {
            $conversation = $this->space->conversations()->save([]);

            $this->isNewConversation = true;
        } else {
            $conversation->save(['update_time' => Date::now()]);
        }

        $this->conversation = $conversation;

        $this->message = $this->conversation->messages()->make([
            'space_id' => $this->space->id,
            'input'    => $input,
        ]);
    }

    protected function initTools()
    {
        $this->addFunction('');
    }

    protected function buildPromptMessages()
    {
        // TODO: Implement buildPromptMessages() method.
    }

    protected function init($params)
    {
        $this->initConversation($params);
        $this->initTools();
    }

    protected function saveMessage($usage, $latency)
    {
        // TODO: Implement saveMessage() method.
    }

    protected function consumeTokens(int $usage): int
    {
        return $usage;
    }

    protected function getClient(): Client
    {
        // TODO: Implement getClient() method.
    }
}
