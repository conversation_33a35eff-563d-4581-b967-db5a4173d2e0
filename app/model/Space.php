<?php

namespace app\model;

use app\job\PrepareRepo;
use app\lib\Agent;
use app\lib\Date;
use app\lib\job\ImportJob;
use app\repo\exception\RepositoryException;
use app\repo\exception\WorkspaceException;
use app\repo\Workspace;
use Closure;
use Exception;
use Firebase\JWT\JWT;
use Hashids\Hashids;
use Symfony\Component\Filesystem\Filesystem;
use think\facade\Queue;
use think\Model;
use think\Request;
use think\Session;
use topthink\git\exception\ReferenceNotFoundException;
use topthink\git\exception\RepositoryNotFoundException;
use topthink\git\Repository;
use yunwuxin\Auth;
use yunwuxin\auth\exception\AuthorizationException;

/**
 * Class app\model\Space
 *
 * @property \app\lib\Date $create_time
 * @property \app\lib\Date $update_time
 * @property int $id
 * @property int $user_id
 * @property string $book_id
 * @property int $status
 * @property-read \app\model\Book $book
 * @property-read \app\model\User $user
 * @property-read mixed $domain
 * @property-read mixed $hash_id
 * @property-read mixed $token
 * @property-read mixed $url
 * @property-read bool $is_ready
 */
class Space extends Model
{
    const STATUS_UNKNOWN   = 0;
    const STATUS_READY     = 1;
    const STATUS_PREPARING = 2;
    const STATUS_FAILED    = -1;

    protected $append = ['hash_id', 'url'];

    public static function get(Book $book, User $user)
    {
        $space = self::where('user_id', $user->id)
            ->where('book_id', $book->id)
            ->find();

        if (empty($space)) {
            $space = self::create([
                'user_id' => $user->id,
                'book_id' => $book->id,
            ]);
        }

        return $space;
    }

    public static function retrieve(Request $request, Session $session, Auth $auth)
    {
        $subDomain = $request->subDomain();

        if ($subDomain == 'sandbox') {
            //沙箱模式
            if ($request->has('id')) {
                $openid = md5($request->param('id'));
                $session->set('sandbox', $openid);

                //TODO 优化session保存机制
                $session->save();
            } else {
                $openid = $session->get('sandbox', $session->getId());
            }
            $user = VirtualUser::createByOpenid($openid);
            return $user->getSpace();
        } else {
            if (!($space = $auth->user())) {
                throw new AuthorizationException('权限不足或者令牌已过期');
            }
            return $space;
        }
    }

    public function conversations()
    {
        return $this->hasMany(Conversation::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class)->withDefault(VirtualUser::createByOpenid($this->book->openid));
    }

    public function book()
    {
        return $this->belongsTo(Book::class);
    }

    public function getAgent()
    {
        return new Agent($this);
    }

    public function getAccessLevel()
    {
        if ($this->user instanceof VirtualUser) {
            return BookMember::OWNER;
        }

        $member = $this->book->members()->attached($this->user);
        if (empty($member)) {
            return 0;
        }
        return $member->access_level;
    }

    public function getClient()
    {
        return $this->user->getClient();
    }

    public function getPoweredBy()
    {
        return $this->getClient()->getPoweredBy($this->book);
    }

    public function getAssetType()
    {
        return $this->getClient()->getAssetType($this->book);
    }

    public function getPresetPlugins()
    {
        return $this->getClient()->getPresetPlugins($this->book);
    }

    public function getRemoteLfsFiles()
    {
        return $this->getClient()->getLfsFiles($this->book);
    }

    public function getSshCommand()
    {
        return $this->getClient()->getSshCommand($this->book);
    }

    public function getRemotePath()
    {
        return $this->getClient()->getRemotePath($this->book);
    }

    public function supportLfs()
    {
        return !empty($this->book->lfs_url);
    }

    public function getAuthor()
    {
        return "{$this->user->getAttr('name')} <{$this->user->getAttr('email')}>";
    }

    public function createWorkspace()
    {
        $repo = $this->getRepo();
        return new Workspace($this, $repo);
    }

    public function createReleaseJob($sha, $message = null)
    {
        $client  = $this->getClient();
        $types   = $client->getReleaseTypes($this->book);
        $timeout = $client->getReleaseTimeout($this->book);

        if (!empty($types)) {
            $release = Release::create([
                'book_id' => $this->book->id,
                'user_id' => $this->user->id,
                'sha'     => $sha,
                'message' => $message,
                'main'    => $types[0],
                'timeout' => $timeout,
            ]);

            foreach ($types as $v) {
                $release->logs()->save([
                    'space_id' => $this->id,
                    'type'     => $v,
                ]);
            }
        }
    }

    public function createImportJob($id, $metadata, $dest)
    {
        $client = $this->user->getClient();

        $timeout = $client->getReleaseTimeout($this->book);

        $data = [
            'id'       => $id,
            'metadata' => $metadata,
            'client'   => $this->getRoom(),
            'dest'     => $dest,
        ];

        cache('import#' . $id, $data, $timeout + 300);

        ImportJob::create($id, $timeout);
    }

    public function checkRepo()
    {
        if ($this->is_ready || $this->status == self::STATUS_FAILED) {
            return true;
        }
        if ($this->status != self::STATUS_PREPARING) {
            $this->save([
                'status' => self::STATUS_PREPARING,
            ]);

            Queue::connection('long')->push(PrepareRepo::class, $this->id, queue: 'repo');
        }
        return false;
    }

    public function getRepo(): Repository
    {
        if ($this->is_ready) {
            $dir = $this->getRepoPath();
            return new Repository($dir);
        }
        throw new RepositoryException();
    }

    public function touch()
    {
        $this->save([
            'update_time' => Date::now(),
        ]);
    }

    public function reset()
    {
        $dir = $this->getRepoPath();

        if (file_exists($dir)) {
            $dest = "/opt/repo/recycle/" . date('Y-m-d') . "/{$this->user_id}_{$this->book_id}_" . date('His');

            $dirname = dirname($dest);
            if (!is_dir($dirname)) {
                mkdir($dirname, 0777, true);
            }

            rename($dir, $dest);
        }
        $this->save([
            'status' => self::STATUS_UNKNOWN,
        ]);
    }

    public function getRoom()
    {
        return "space_{$this->id}";
    }

    public function getRepoPath()
    {
        return "/opt/repo/{$this->user_id}/{$this->book_id}";
    }

    protected function getIsOnlineAttr()
    {
        return $this->update_time->isAfter('-1 minutes');
    }

    protected function getIsReadyAttr()
    {
        $dir = $this->getRepoPath();
        return $this->status == self::STATUS_READY || ($this->status == self::STATUS_UNKNOWN && file_exists($dir));
    }

    protected function getDomainAttr()
    {
        $appHost = config('app.host');
        $host    = parse_url($appHost, PHP_URL_HOST);

        return "{$this->hash_id}.{$host}";
    }

    protected function getHashIdAttr()
    {
        $appHost = config('app.host');
        $hashids = new Hashids($appHost, 10, 'abcdefghijklmnopqrstuvwxyz123456789');
        return $hashids->encode($this->id);
    }

    protected function getUrlAttr()
    {
        return (string) url('/')->domain($this->domain);
    }

    protected function getTokenAttr()
    {
        return JWT::encode([
            'exp' => time() + 60 * 5,
            'iat' => time(),
            'id'  => $this->id,
        ], $this->hash_id, 'HS256');
    }
}
